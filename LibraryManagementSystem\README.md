# نظام إدارة المكتبة - Library Management System

نظام شامل لإدارة المكتبات مبني بتقنية ASP.NET Core مع Razor Pages، يدعم اللغة العربية والإنجليزية.

## المميزات الرئيسية

### 🔍 البحث والاستعراض
- البحث المتقدم في الكتب بالعنوان، المؤلف، أو الرقم المعياري
- فلترة الكتب حسب النوع والتوفر
- عرض تفاصيل الكتب مع معلومات شاملة
- إحصائيات سريعة للمكتبة

### 📚 إدارة الاستعارات
- استعارة الكتب المتاحة
- إرجاع الكتب مع حساب الرسوم المتأخرة
- تمديد فترة الاستعارة
- عرض الاستعارات النشطة والمتأخرة
- إحصائيات الاستعارات

### ⚡ الأداء والتخزين المؤقت
- تخزين مؤقت ذكي للبيانات المتكررة
- فهرسة قاعدة البيانات للبحث السريع
- تحسين الاستعلامات للأداء العالي

### 🎨 واجهة المستخدم
- تصميم متجاوب يدعم جميع الأجهزة
- دعم كامل للغة العربية (RTL)
- واجهة حديثة باستخدام Bootstrap 5
- رسائل تفاعلية وتنبيهات

## البنية التقنية

### طبقة البيانات (DAL)
- **Models**: نماذج البيانات (Book, User, Borrowing)
- **DTOs**: كائنات نقل البيانات للبحث والإحصائيات
- **Repositories**: مستودعات البيانات مع التخزين المؤقت
- **Caching**: خدمة التخزين المؤقت باستخدام MemoryCache
- **Database**: إدارة الاتصال وتهيئة قاعدة البيانات

### طبقة منطق الأعمال (BLL)
- **BookService**: خدمات إدارة الكتب
- **BorrowingService**: خدمات إدارة الاستعارات
- **ServiceResult**: نمط موحد لإرجاع النتائج
- **LibrarySettings**: إعدادات المكتبة القابلة للتخصيص

### طبقة العرض (UI)
- **Razor Pages**: صفحات ويب تفاعلية
- **Bootstrap 5**: تصميم متجاوب وحديث
- **Font Awesome**: أيقونات احترافية
- **JavaScript**: تفاعلات العميل

## متطلبات التشغيل

- .NET 9.0 أو أحدث
- SQL Server LocalDB أو SQL Server
- Visual Studio 2022 أو VS Code

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd LibraryManagementSystem
```

### 2. استعادة الحزم
```bash
dotnet restore
```

### 3. تحديث سلسلة الاتصال
قم بتحديث `appsettings.json` في مشروع UI:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=LibraryManagementSystem;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### 4. تشغيل التطبيق
```bash
dotnet run --project LibraryManagementSystem.UI
```

سيتم إنشاء قاعدة البيانات وإدراج البيانات الأولية تلقائياً عند التشغيل الأول.

## تشغيل الاختبارات

```bash
dotnet test LibraryManagementSystem.Tests
```

## الإعدادات

### إعدادات المكتبة (LibrarySettings)
```json
{
  "LibrarySettings": {
    "MaxBooksPerUser": 5,
    "DefaultBorrowingDays": 14,
    "LateFeePerDay": 1.00,
    "GraceDays": 0,
    "MaxRenewalCount": 2,
    "RenewalDays": 14,
    "LibraryName": "مكتبة الجامعة المركزية",
    "ContactEmail": "<EMAIL>",
    "ContactPhone": "+966-11-1234567"
  }
}
```

### إعدادات التخزين المؤقت
```json
{
  "CacheSettings": {
    "DefaultExpirationMinutes": 30,
    "SearchResultsExpirationMinutes": 15,
    "StatisticsExpirationMinutes": 60
  }
}
```

## البيانات الأولية

يتضمن النظام بيانات أولية:
- 8 مستخدمين تجريبيين
- 20 كتاب في مختلف المجالات
- فهارس محسنة للأداء

## الصفحات الرئيسية

### الصفحة الرئيسية (`/`)
- إحصائيات سريعة
- البحث السريع
- الكتب الأكثر استعارة
- روابط سريعة

### البحث عن الكتب (`/Books`)
- نموذج بحث متقدم
- عرض النتائج مع التقسيم على صفحات
- فلترة وترتيب النتائج
- استعارة مباشرة للكتب المتاحة

### إدارة الاستعارات (`/Borrowings`)
- عرض جميع الاستعارات
- فلترة حسب المستخدم والحالة
- إرجاع وتمديد الاستعارات
- إحصائيات الاستعارات

## المساهمة

نرحب بالمساهمات! يرجى:
1. إنشاء fork للمشروع
2. إنشاء branch للميزة الجديدة
3. إضافة الاختبارات المناسبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للدعم والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-1234567

---

**ملاحظة**: هذا النظام مصمم للأغراض التعليمية والتطويرية. للاستخدام في بيئة الإنتاج، يُنصح بإضافة مميزات الأمان والمصادقة المناسبة.
