using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace LibraryManagementSystem.UI.Pages.Auth
{
    /// <summary>
    /// نموذج صفحة تسجيل الخروج
    /// Logout page model
    /// </summary>
    public class LogoutModel : PageModel
    {
        private readonly ILogger<LogoutModel> _logger;

        public LogoutModel(ILogger<LogoutModel> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// معالج طلب GET - تسجيل الخروج
        /// GET request handler - logout
        /// </summary>
        public IActionResult OnGet()
        {
            return OnPost();
        }

        /// <summary>
        /// معالج طلب POST - تسجيل الخروج
        /// POST request handler - logout
        /// </summary>
        public IActionResult OnPost()
        {
            try
            {
                var userEmail = HttpContext.Session.GetString("UserEmail");
                
                // مسح جميع بيانات الجلسة
                // Clear all session data
                HttpContext.Session.Clear();

                _logger.LogInformation("تم تسجيل الخروج بنجاح للمستخدم: {Email} - Successful logout for user: {Email}", 
                    userEmail ?? "Unknown", userEmail ?? "Unknown");

                TempData["SuccessMessage"] = "تم تسجيل الخروج بنجاح - Successfully logged out";

                return RedirectToPage("/Auth/Login");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل الخروج - Error during logout");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تسجيل الخروج - An error occurred during logout";
                return RedirectToPage("/Auth/Login");
            }
        }
    }
}
