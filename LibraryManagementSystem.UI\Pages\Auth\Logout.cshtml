@page
@model LibraryManagementSystem.UI.Pages.Auth.LogoutModel
@{
    ViewData["Title"] = "تسجيل الخروج - Logout";
    Layout = "_Layout";
}

<div class="container-fluid vh-100" dir="rtl">
    <div class="row h-100 justify-content-center align-items-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-warning text-dark text-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج - Logout
                    </h3>
                </div>
                <div class="card-body p-4 text-center">
                    <div class="mb-4">
                        <i class="fas fa-question-circle fa-4x text-warning mb-3"></i>
                        <h4>هل أنت متأكد من تسجيل الخروج؟</h4>
                        <p class="text-muted">Are you sure you want to logout?</p>

                        @if (!string.IsNullOrEmpty(Model.CurrentUserName))
                        {
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-user me-2"></i>
                                <strong>المستخدم الحالي - Current User:</strong> @Model.CurrentUserName
                                @if (!string.IsNullOrEmpty(Model.CurrentUserEmail))
                                {
                                    <br>

                                    <small class="text-muted">@Model.CurrentUserEmail</small>
                                }
                            </div>
                        }

                        <p class="small text-muted">
                            سيتم إنهاء جلستك الحالية وستحتاج إلى تسجيل الدخول مرة أخرى للوصول إلى النظام.
                            <br>
                            Your current session will be ended and you'll need to login again to access the system.
                        </p>
                    </div>

                    <div class="d-grid gap-2">
                        <form method="post" class="d-inline">
                            <button type="submit" class="btn btn-warning btn-lg w-100 mb-2">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                نعم، تسجيل الخروج - Yes, Logout
                            </button>
                        </form>

                        <a href="/Books/Index" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء والعودة - Cancel & Go Back
                        </a>
                    </div>
                </div>

                <div class="card-footer text-center bg-light">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        جلستك آمنة ومحمية - Your session is secure and protected
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
    }

    .btn {
        border-radius: 8px;
    }

    .fa-question-circle {
        animation: pulse 2s infinite;
    }

    @@keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }

        100% {
            transform: scale(1);
        }
    }

    .card-footer {
        border-radius: 0 0 15px 15px !important;
    }
</style>
